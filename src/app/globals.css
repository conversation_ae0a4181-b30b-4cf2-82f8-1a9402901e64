@import "tailwindcss";

:root {
  /* 🎨 HSSL New Color System */
  --background: rgb(242, 232, 216);
  --foreground: #2d2d2d;

  /* Primary Brand Colors - Muted Red/Rust (Main Actions) */
  --primary: rgb(186, 78, 78);
  --primary-foreground: #ffffff;
  --primary-50: rgb(252, 245, 245);
  --primary-100: rgb(248, 230, 230);
  --primary-200: rgb(240, 205, 205);
  --primary-300: rgb(230, 165, 165);
  --primary-400: rgb(210, 120, 120);
  --primary-500: rgb(186, 78, 78);
  --primary-600: rgb(166, 68, 68);
  --primary-700: rgb(146, 58, 58);
  --primary-800: rgb(126, 48, 48);
  --primary-900: rgb(106, 38, 38);

  /* Secondary Brand Colors - Sage Green (Supporting Actions) */
  --secondary: rgb(176, 186, 154);
  --secondary-foreground: #ffffff;
  --secondary-50: rgb(248, 250, 245);
  --secondary-100: rgb(240, 245, 235);
  --secondary-200: rgb(225, 235, 215);
  --secondary-300: rgb(200, 215, 185);
  --secondary-400: rgb(188, 200, 170);
  --secondary-500: rgb(176, 186, 154);
  --secondary-600: rgb(156, 166, 134);
  --secondary-700: rgb(136, 146, 114);
  --secondary-800: rgb(116, 126, 94);
  --secondary-900: rgb(96, 106, 74);

  /* Success Colors - Sage Green */
  --success: rgb(176, 186, 154);
  --success-50: rgb(248, 250, 245);
  --success-100: rgb(240, 245, 235);
  --success-200: rgb(225, 235, 215);
  --success-300: rgb(200, 215, 185);
  --success-400: rgb(188, 200, 170);
  --success-500: rgb(176, 186, 154);
  --success-600: rgb(156, 166, 134);
  --success-700: rgb(136, 146, 114);
  --success-800: rgb(116, 126, 94);
  --success-900: rgb(96, 106, 74);

  /* Warning Colors - Light Beige/Cream */
  --warning: rgb(242, 232, 216);
  --warning-50: rgb(254, 252, 250);
  --warning-100: rgb(250, 245, 235);
  --warning-200: rgb(246, 238, 220);
  --warning-300: rgb(242, 232, 216);
  --warning-400: rgb(235, 220, 200);
  --warning-500: rgb(228, 208, 184);
  --warning-600: rgb(220, 195, 168);
  --warning-700: rgb(210, 180, 150);
  --warning-800: rgb(200, 165, 132);
  --warning-900: rgb(190, 150, 114);

  /* Error Colors - Muted Red/Rust */
  --error: rgb(186, 78, 78);
  --error-50: rgb(252, 245, 245);
  --error-100: rgb(248, 230, 230);
  --error-200: rgb(240, 205, 205);
  --error-300: rgb(230, 165, 165);
  --error-400: rgb(210, 120, 120);
  --error-500: rgb(186, 78, 78);
  --error-600: rgb(166, 68, 68);
  --error-700: rgb(146, 58, 58);
  --error-800: rgb(126, 48, 48);
  --error-900: rgb(106, 38, 38);

  /* Accent Colors - Light Beige/Cream */
  --accent: rgb(242, 232, 216);
  --accent-foreground: #2d2d2d;
  --accent-50: rgb(254, 252, 250);
  --accent-100: rgb(250, 245, 235);
  --accent-200: rgb(246, 238, 220);
  --accent-300: rgb(242, 232, 216);
  --accent-400: rgb(235, 220, 200);
  --accent-500: rgb(228, 208, 184);
  --accent-600: rgb(220, 195, 168);
  --accent-700: rgb(210, 180, 150);
  --accent-800: rgb(200, 165, 132);
  --accent-900: rgb(190, 150, 114);

  /* Neutral Colors - Warm Grays */
  --neutral: #6b6b6b;
  --neutral-50: rgb(250, 248, 245);
  --neutral-100: rgb(245, 240, 235);
  --neutral-200: rgb(235, 225, 215);
  --neutral-300: rgb(220, 205, 190);
  --neutral-400: rgb(180, 165, 150);
  --neutral-500: #6b6b6b;
  --neutral-600: #5a5a5a;
  --neutral-700: #4a4a4a;
  --neutral-800: #3a3a3a;
  --neutral-900: #2d2d2d;

  /* Gray Scale - Warm Tones */
  --gray-50: rgb(250, 248, 245);
  --gray-100: rgb(245, 240, 235);
  --gray-200: rgb(235, 225, 215);
  --gray-300: rgb(220, 205, 190);
  --gray-400: rgb(180, 165, 150);
  --gray-500: #6b6b6b;
  --gray-600: #5a5a5a;
  --gray-700: #4a4a4a;
  --gray-800: #3a3a3a;
  --gray-900: #2d2d2d;

  /* Spacing Scale */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  --spacing-4xl: 6rem;     /* 96px */

  /* Typography Scale */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  --text-4xl: 2.25rem;     /* 36px */
  --text-5xl: 3rem;        /* 48px */
  --text-6xl: 3.75rem;     /* 60px */

  /* Border Radius */
  --radius-sm: 0.25rem;    /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(245, 240, 235);
}

::-webkit-scrollbar-thumb {
  background: rgb(186, 78, 78);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(166, 68, 68);
}

/* 🎨 Enhanced Focus Styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  --tw-ring-color: rgb(186, 78, 78);
}

.focus-ring-secondary {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  --tw-ring-color: rgb(176, 186, 154);
}

.focus-ring-success {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  --tw-ring-color: rgb(176, 186, 154);
}

.focus-ring-warning {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  --tw-ring-color: rgb(242, 232, 216);
}

.focus-ring-error {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  --tw-ring-color: rgb(186, 78, 78);
}

/* 🌈 Gradient Utilities */
.gradient-hero-primary {
  background: linear-gradient(135deg, rgb(186, 78, 78) 0%, rgb(176, 186, 154) 50%, rgb(242, 232, 216) 100%);
}

.gradient-hero-secondary {
  background: linear-gradient(135deg, rgb(176, 186, 154) 0%, rgb(242, 232, 216) 50%, rgb(186, 78, 78) 100%);
}

.gradient-card-subtle {
  background: linear-gradient(135deg, rgb(248, 250, 245) 0%, rgb(242, 232, 216) 50%, rgb(250, 245, 235) 100%);
}

.gradient-interactive-primary {
  background: linear-gradient(135deg, rgb(186, 78, 78) 0%, rgb(176, 186, 154) 100%);
}

.gradient-interactive-hover {
  background: linear-gradient(135deg, rgb(166, 68, 68) 0%, rgb(156, 166, 134) 100%);
}

/* 🃏 Card Component Styles */
.card-default {
  background: rgb(242, 232, 216);
  border: 1px solid rgb(235, 225, 215);
  @apply rounded-lg shadow-sm hover:shadow-md transition-all duration-200;
}

.card-gradient {
  background: linear-gradient(135deg, rgb(242, 232, 216) 0%, rgb(248, 250, 245) 100%);
  border: 1px solid rgb(240, 245, 235);
  @apply rounded-lg shadow-sm hover:shadow-lg transition-all duration-200;
}

.card-elevated {
  background: rgb(242, 232, 216);
  border: 1px solid rgb(235, 225, 215);
  @apply rounded-lg shadow-lg hover:shadow-xl transition-all duration-200;
}

.card-primary {
  background: rgb(252, 245, 245);
  border: 1px solid rgb(240, 205, 205);
  color: rgb(106, 38, 38);
  @apply rounded-lg transition-all duration-200;
}
.card-primary:hover {
  background: rgb(248, 230, 230);
  border-color: rgb(230, 165, 165);
}

.card-secondary {
  background: rgb(248, 250, 245);
  border: 1px solid rgb(225, 235, 215);
  color: rgb(96, 106, 74);
  @apply rounded-lg transition-all duration-200;
}
.card-secondary:hover {
  background: rgb(240, 245, 235);
  border-color: rgb(200, 215, 185);
}

/* 🔘 Button Component Styles */
.btn-primary {
  background: rgb(186, 78, 78);
  color: white;
  border: 1px solid rgb(186, 78, 78);
  @apply focus:ring-2 focus:ring-offset-2 disabled:opacity-50 transition-all duration-200;
  --tw-ring-color: rgb(186, 78, 78);
}
.btn-primary:hover {
  background: rgb(166, 68, 68);
  border-color: rgb(166, 68, 68);
}
.btn-primary:active {
  background: rgb(146, 58, 58);
}

.btn-secondary {
  background: rgb(240, 245, 235);
  color: rgb(96, 106, 74);
  border: 1px solid rgb(225, 235, 215);
  @apply focus:ring-2 focus:ring-offset-2 disabled:opacity-50 transition-all duration-200;
  --tw-ring-color: rgb(176, 186, 154);
}
.btn-secondary:hover {
  background: rgb(225, 235, 215);
  border-color: rgb(200, 215, 185);
}
.btn-secondary:active {
  background: rgb(200, 215, 185);
}

.btn-outline {
  background: transparent;
  color: rgb(186, 78, 78);
  border: 1px solid rgb(186, 78, 78);
  @apply focus:ring-2 focus:ring-offset-2 disabled:opacity-50 transition-all duration-200;
  --tw-ring-color: rgb(186, 78, 78);
}
.btn-outline:hover {
  background: rgb(252, 245, 245);
  border-color: rgb(166, 68, 68);
  color: rgb(166, 68, 68);
}
.btn-outline:active {
  background: rgb(248, 230, 230);
}

.btn-ghost {
  background: transparent;
  color: rgb(186, 78, 78);
  border: 1px solid transparent;
  @apply focus:ring-2 focus:ring-offset-2 disabled:opacity-50 transition-all duration-200;
  --tw-ring-color: rgb(186, 78, 78);
}
.btn-ghost:hover {
  background: rgb(252, 245, 245);
  color: rgb(166, 68, 68);
}
.btn-ghost:active {
  background: rgb(248, 230, 230);
}

/* 📱 Responsive Design Utilities */
.section-padding {
  @apply py-16 lg:py-24;
}

.section-padding-lg {
  @apply py-24 lg:py-32;
}

.container-responsive {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

/* Optimized animations using transform and opacity only */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Hardware acceleration for smooth animations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

/* Smooth transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Focus styles for accessibility */
.focus-visible:focus {
  outline: 2px solid rgb(186, 78, 78);
  outline-offset: 2px;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
