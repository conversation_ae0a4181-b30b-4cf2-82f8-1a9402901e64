// 🎨 HSSL Color System Utilities
// Comprehensive color management for organic, eco-friendly design

import { colorTheme, gradientSystem, sectionPatterns } from './animations'

// 🌱 Color Role Definitions
export const colorRoles = {
  // Primary brand colors
  brand: {
    primary: 'green-400',      // Sage green - main color
    secondary: 'yellow-100',   // Light beige/cream - supporting
    accent: 'red-400'          // Muted red - important only
  },

  // Semantic colors
  semantic: {
    success: 'green-400',      // Sage green
    warning: 'green-300',      // Lighter sage green for mild warnings
    error: 'red-400',          // Muted red for important errors only
    info: 'yellow-200'         // Light beige/cream
  },

  // UI element colors
  ui: {
    background: 'yellow-50',   // Light beige/cream background
    surface: 'yellow-100',     // Slightly darker cream for surfaces
    border: 'yellow-200',      // Cream borders
    text: {
      primary: 'stone-800',    // Dark text
      secondary: 'stone-600',  // Medium text
      muted: 'stone-500'       // Light text
    }
  }
}

// 🎯 Button Color Variants (New Color System)
export const buttonVariants = {
  // Primary action buttons - Sage Green
  primary: {
    base: 'text-white border',
    hover: '',
    active: '',
    focus: 'focus:ring-2 focus:ring-offset-2',
    disabled: 'disabled:opacity-50',
    style: {
      backgroundColor: 'rgb(176, 186, 154)',
      borderColor: 'rgb(176, 186, 154)',
      '--tw-ring-color': 'rgb(176, 186, 154)'
    }
  },

  // Secondary action buttons - Light Beige/Cream
  secondary: {
    base: 'border',
    hover: '',
    active: '',
    focus: 'focus:ring-2 focus:ring-offset-2',
    disabled: 'disabled:opacity-50',
    style: {
      backgroundColor: 'rgb(242, 232, 216)',
      color: 'rgb(96, 106, 74)',
      borderColor: 'rgb(220, 195, 168)',
      '--tw-ring-color': 'rgb(176, 186, 154)'
    }
  },

  // Outline buttons - Sage Green
  outline: {
    base: 'bg-transparent border',
    hover: '',
    active: '',
    focus: 'focus:ring-2 focus:ring-offset-2',
    disabled: 'disabled:opacity-50',
    style: {
      color: 'rgb(176, 186, 154)',
      borderColor: 'rgb(176, 186, 154)',
      '--tw-ring-color': 'rgb(176, 186, 154)'
    }
  },

  // Ghost buttons - Sage Green
  ghost: {
    base: 'bg-transparent border-transparent',
    hover: '',
    active: '',
    focus: 'focus:ring-2 focus:ring-offset-2',
    disabled: 'disabled:opacity-50',
    style: {
      color: 'rgb(176, 186, 154)',
      '--tw-ring-color': 'rgb(176, 186, 154)'
    }
  },

  // Success buttons - Sage Green
  success: {
    base: 'text-white border',
    hover: '',
    active: '',
    focus: 'focus:ring-2 focus:ring-offset-2',
    disabled: 'disabled:opacity-50',
    style: {
      backgroundColor: 'rgb(176, 186, 154)',
      borderColor: 'rgb(176, 186, 154)',
      '--tw-ring-color': 'rgb(176, 186, 154)'
    }
  },
  
  // Warning buttons - Sage Green (mild warnings)
  warning: {
    base: 'text-white border',
    hover: '',
    active: '',
    focus: 'focus:ring-2 focus:ring-offset-2',
    disabled: 'disabled:opacity-50',
    style: {
      backgroundColor: 'rgb(176, 186, 154)',
      borderColor: 'rgb(176, 186, 154)',
      '--tw-ring-color': 'rgb(176, 186, 154)'
    }
  },

  // Error/destructive buttons - Muted Red (IMPORTANT ONLY)
  destructive: {
    base: 'text-white border',
    hover: '',
    active: '',
    focus: 'focus:ring-2 focus:ring-offset-2',
    disabled: 'disabled:opacity-50',
    style: {
      backgroundColor: 'rgb(186, 78, 78)',
      borderColor: 'rgb(186, 78, 78)',
      '--tw-ring-color': 'rgb(186, 78, 78)'
    }
  }
}

// 🃏 Card Background Variants
export const cardVariants = {
  // Default white card
  default: {
    base: 'bg-white border-slate-200',
    hover: 'hover:bg-slate-50 hover:border-slate-300',
    shadow: 'shadow-sm hover:shadow-md'
  },
  
  // Subtle gradient cards
  gradient: {
    base: 'bg-gradient-to-br from-white to-emerald-50 border-emerald-100',
    hover: 'hover:from-emerald-50 hover:to-emerald-100 hover:border-emerald-200',
    shadow: 'shadow-sm hover:shadow-lg'
  },
  
  // Elevated cards
  elevated: {
    base: 'bg-white border-slate-200',
    hover: 'hover:bg-slate-50',
    shadow: 'shadow-lg hover:shadow-xl'
  },
  
  // Colored cards
  colored: {
    emerald: {
      base: 'bg-emerald-50 border-emerald-200 text-emerald-900',
      hover: 'hover:bg-emerald-100 hover:border-emerald-300'
    },
    teal: {
      base: 'bg-teal-50 border-teal-200 text-teal-900',
      hover: 'hover:bg-teal-100 hover:border-teal-300'
    },
    green: {
      base: 'bg-green-50 border-green-200 text-green-900',
      hover: 'hover:bg-green-100 hover:border-green-300'
    }
  }
}

// 🎨 Focus Ring Styles
export const focusRings = {
  primary: 'focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2',
  secondary: 'focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2',
  success: 'focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2',
  warning: 'focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2',
  error: 'focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
  neutral: 'focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2'
}

// 🌈 Utility Functions
export const colorUtils = {
  // Get complete button classes
  getButtonClasses: (variant: keyof typeof buttonVariants) => {
    const v = buttonVariants[variant]
    return `${v.base} ${v.hover} ${v.active} ${v.focus} ${v.disabled} transition-all duration-200`
  },
  
  // Get complete card classes
  getCardClasses: (variant: keyof typeof cardVariants, colored?: keyof typeof cardVariants.colored) => {
    if (variant === 'colored' && colored) {
      const c = cardVariants.colored[colored]
      return `${c.base} ${c.hover} border rounded-lg transition-all duration-200`
    }
    const v = cardVariants[variant as keyof Omit<typeof cardVariants, 'colored'>]
    return `${v.base} ${v.hover} ${v.shadow} border rounded-lg transition-all duration-200`
  },
  
  // Get section background by index
  getSectionBackground: (index: number, pattern: keyof typeof sectionPatterns = 'standard') => {
    const patterns = sectionPatterns[pattern]
    return patterns[index % patterns.length]
  },
  
  // Get gradient by use case
  getGradient: (useCase: keyof typeof gradientSystem, variant: string = 'primary') => {
    const gradients = gradientSystem[useCase]
    if (typeof gradients === 'object' && gradients !== null) {
      const gradientsObj = gradients as Record<string, string | Record<string, string>>
      const result = gradientsObj[variant] || gradientsObj.primary || Object.values(gradients)[0]
      return typeof result === 'string' ? result : Object.values(result)[0] || ''
    }
    return typeof gradients === 'string' ? gradients : ''
  }
}

// 📋 Color Accessibility Guidelines
export const accessibilityGuidelines = {
  // Minimum contrast ratios (WCAG 2.1)
  contrast: {
    normal: 4.5, // AA standard
    large: 3,    // AA standard for large text
    enhanced: 7  // AAA standard
  },
  
  // Recommended color combinations
  combinations: {
    highContrast: [
      { bg: 'white', text: 'slate-900' },
      { bg: 'emerald-600', text: 'white' },
      { bg: 'slate-900', text: 'white' }
    ],
    mediumContrast: [
      { bg: 'emerald-50', text: 'emerald-900' },
      { bg: 'teal-50', text: 'teal-900' },
      { bg: 'slate-100', text: 'slate-800' }
    ]
  }
}

// Export everything for easy access
export { colorTheme, gradientSystem, sectionPatterns }
